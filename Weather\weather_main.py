"""
Главный модуль Telegram-бота для прогноза погоды.

Точка входа приложения. Настраивает логирование, загружает конфигурацию,
инициализирует объект Bot и JobQueue, регистрирует обработчики обновлений,
запускает планировщик и запускает приложение.

Этап 10 плана разработки.
"""

import logging
import asyncio
import sys
from typing import Optional

# Импорт компонентов Telegram Bot API
from telegram import Bot
from telegram.ext import Application, MessageHandler, filters
from telegram.constants import ParseMode

# Импорт модулей проекта
from weather_config import (
    TELEGRAM_TOKEN, CHANNEL_ID, validate_config
)
from weather_storage import Storage
from weather_handlers import on_channel_post
from weather_scheduler import schedule_updates

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('weather_bot.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# Глобальная переменная для отслеживания состояния бота
_bot_running = False


def main() -> None:
    """
    Основная функция приложения.

    Выполняет:
    1. Валидацию конфигурации
    2. Инициализацию хранилища
    3. Создание и настройку Application
    4. Регистрацию обработчиков
    5. Планирование задач обновления (если есть сохраненные message_id)
    6. Запуск бота
    """
    logger.info("🚀 Запуск Telegram-бота погоды...")

    # 1. Валидация конфигурации
    logger.info("📋 Проверка конфигурации...")
    if not validate_config():
        logger.error("❌ Ошибка конфигурации. Завершение работы.")
        sys.exit(1)
    logger.info("✅ Конфигурация валидна")

    # 2. Инициализация хранилища
    logger.info("💾 Инициализация хранилища...")
    storage = Storage()
    logger.info("✅ Хранилище инициализировано")

    # 3. Создание экземпляра Application
    logger.info("🤖 Создание Application...")
    application = Application.builder().token(TELEGRAM_TOKEN).build()

    # Настройка defaults для HTML-форматирования
    application.bot_data['parse_mode'] = ParseMode.HTML
    logger.info("✅ Application создан")

    # 4. Регистрация обработчиков
    logger.info("📡 Регистрация обработчиков...")

    # Обработчик постов в канале (используем MessageHandler с фильтром для каналов)
    channel_post_handler = MessageHandler(
        filters.ChatType.CHANNEL & filters.TEXT,
        on_channel_post
    )
    application.add_handler(channel_post_handler)

    logger.info("✅ Обработчики зарегистрированы")

    # 5. Планирование задач обновления (если есть сохраненные message_id)
    logger.info("⏰ Проверка сохраненных сообщений для планирования обновлений...")

    # Проверяем, есть ли сохраненные message_id
    has_messages = False
    saved_channel_id = storage.get_channel_id()

    logger.info(f"📋 Проверка сохраненного состояния:")
    logger.info(f"   - Сохраненный channel_id: {saved_channel_id}")

    for msg_type in ['3days', 'today', 'current']:
        message_id = storage.get_message_id(msg_type)
        if message_id:
            has_messages = True
            logger.info(f"   - Найдено сообщение '{msg_type}': message_id={message_id}")
        else:
            logger.info(f"   - Сообщение '{msg_type}': не найдено")

    if has_messages:
        logger.info("🔄 Планирование задач обновления...")
        success = schedule_updates(application, storage)
        if success:
            logger.info("✅ Задачи обновления запланированы успешно")
        else:
            logger.warning("⚠️ Не удалось запланировать некоторые задачи обновления")
    else:
        logger.info("📭 Сохраненных сообщений не найдено. Планировщик будет запущен после первой публикации.")

    # 6. Запуск бота
    logger.info("🎯 Запуск polling...")
    logger.info(f"📢 Бот будет отслеживать канал: {CHANNEL_ID}")
    logger.info("🔍 Ожидание сообщений с ключевым словом 'weather'...")

    try:
        # Запуск polling
        application.run_polling(
            allowed_updates=['channel_post'],  # Отслеживаем только посты в каналах
            drop_pending_updates=True  # Игнорируем старые обновления при запуске
        )
    except KeyboardInterrupt:
        logger.info("⏹️ Получен сигнал остановки")
    except Exception as e:
        logger.error(f"💥 Критическая ошибка: {e}")
        raise
    finally:
        logger.info("🛑 Завершение работы бота")


async def async_main() -> None:
    """
    Асинхронная версия основной функции приложения.

    Выполняет те же действия что и main(), но с асинхронным запуском бота.
    """
    global _bot_running

    # Проверяем, не запущен ли уже бот
    if _bot_running:
        logger.warning("⚠️ Бот уже запущен! Игнорируем повторный запуск.")
        return

    _bot_running = True
    logger.info("🚀 Запуск Telegram-бота погоды (асинхронно)...")

    try:
        # 1. Валидация конфигурации
        logger.info("📋 Проверка конфигурации...")
        if not validate_config():
            logger.error("❌ Ошибка конфигурации. Завершение работы.")
            return
        logger.info("✅ Конфигурация валидна")

        # 2. Инициализация хранилища
        logger.info("💾 Инициализация хранилища...")
        storage = Storage()
        logger.info("✅ Хранилище инициализировано")

        # 3. Создание экземпляра Application
        logger.info("🤖 Создание Application...")
        application = Application.builder().token(TELEGRAM_TOKEN).build()

        # Настройка defaults для HTML-форматирования
        application.bot_data['parse_mode'] = ParseMode.HTML
        logger.info("✅ Application создан")

        # 4. Регистрация обработчиков
        logger.info("📡 Регистрация обработчиков...")

        # Обработчик постов в канале (используем MessageHandler с фильтром для каналов)
        channel_post_handler = MessageHandler(
            filters.ChatType.CHANNEL & filters.TEXT,
            on_channel_post
        )
        application.add_handler(channel_post_handler)

        logger.info("✅ Обработчики зарегистрированы")

        # 5. Планирование задач обновления (если есть сохраненные message_id)
        logger.info("⏰ Проверка сохраненных сообщений для планирования обновлений...")

        # Проверяем, есть ли сохраненные message_id
        has_messages = False
        saved_channel_id = storage.get_channel_id()

        logger.info(f"📋 Проверка сохраненного состояния:")
        logger.info(f"   - Сохраненный channel_id: {saved_channel_id}")

        for msg_type in ['3days', 'today', 'current']:
            message_id = storage.get_message_id(msg_type)
            if message_id:
                has_messages = True
                logger.info(f"   - Найдено сообщение '{msg_type}': message_id={message_id}")
            else:
                logger.info(f"   - Сообщение '{msg_type}': не найдено")

        if has_messages:
            logger.info("🔄 Планирование задач обновления...")
            success = schedule_updates(application, storage)
            if success:
                logger.info("✅ Задачи обновления запланированы успешно")
            else:
                logger.warning("⚠️ Не удалось запланировать некоторые задачи обновления")
        else:
            logger.info("📭 Сохраненных сообщений не найдено. Планировщик будет запущен после первой публикации.")

    # 6. Запуск бота
    application_started = False
    try:
        logger.info("🎯 Запуск polling...")
        logger.info(f"📢 Бот будет отслеживать канал: {CHANNEL_ID}")
        logger.info("🔍 Ожидание сообщений с ключевым словом 'weather'...")
        
        # Асинхронный запуск polling
        async with application:
            await application.initialize()
            await application.start()
            application_started = True
            await application.updater.start_polling(
                allowed_updates=['channel_post'],  # Отслеживаем только посты в каналах
                drop_pending_updates=True  # Игнорируем старые обновления при запуске
            )
            # Ждем бесконечно, пока не будет сигнала остановки
            await application.updater.idle()
    except asyncio.CancelledError:
        logger.info("🛑 Получен сигнал отмены задачи")
        raise
    except Exception as e:
        logger.error(f"💥 Критическая ошибка: {e}")
        raise
    finally:
        if application_started:
            try:
                await application.stop()
                logger.info("✅ Application корректно остановлен")
            except Exception as e:
                logger.error(f"⚠️ Ошибка при остановке Application: {e}")
        _bot_running = False
        logger.info("🛑 Завершение работы бота")


def run_bot() -> None:
    """
    Точка входа для запуска бота.
    Обертка для запуска синхронной main() функции.
    """
    try:
        main()
    except KeyboardInterrupt:
        logger.info("👋 Бот остановлен пользователем")
    except Exception as e:
        logger.error(f"💥 Фатальная ошибка: {e}")
        sys.exit(1)


async def run_bot_async() -> None:
    """
    Асинхронная точка входа для запуска бота.
    Обертка для запуска асинхронной async_main() функции.
    """
    try:
        await async_main()
    except asyncio.CancelledError:
        logger.info("👋 Бот остановлен через отмену задачи")
        raise
    except KeyboardInterrupt:
        logger.info("👋 Бот остановлен пользователем")
    except Exception as e:
        logger.error(f"💥 Фатальная ошибка: {e}")
        raise


if __name__ == "__main__":
    run_bot()
